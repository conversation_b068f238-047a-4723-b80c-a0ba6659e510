/**
 * Statistics Page for Disc Golf Inventory Management System
 *
 * Comprehensive collection analytics with interactive charts, responsive design,
 * and advanced metrics following EARS requirements FR-STATS-001, FR-STATS-002, FR-STATS-003.
 */

"use client";

import * as React from "react";
import { Suspense } from "react";
import { Layout, PageContainer, Section } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { EmptyState } from "@/components/ui/EmptyState";
import { StatCard, StatCardGrid } from "@/components/dashboard/StatCard";
import { DistributionChart, DistributionGrid } from "@/components/dashboard/DistributionChart";
import { useInventory } from "@/hooks/useInventory";
import {
  calculateCollectionValue,
  calculateAverageWeight,
  getMostCommonManufacturer,
  countDiscsByCondition,
  countDiscsByLocation,
  countDiscsByManufacturer,
  calculateAverageFlightNumbers,
} from "@/lib/discUtils";
import type { Disc, DiscCondition, Location } from "@/lib/types";
import {
  BarChart3,
  Disc3,
  DollarSign,
  Weight,
  TrendingUp,
  Package,
  MapPin,
  Star,
  Target,
  Zap,
  Plus,
} from "lucide-react";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface DistributionItem {
  label: string;
  count: number;
  percentage: number;
  color?: string;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Convert count data to distribution items with percentages
 */
function createDistributionData(
  countData: Record<string, number>,
  total: number
): DistributionItem[] {
  return Object.entries(countData)
    .map(([label, count]) => ({
      label,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0,
    }))
    .sort((a, b) => b.count - a.count);
}

/**
 * Format condition labels for display
 */
function formatConditionLabel(condition: string): string {
  return condition
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

/**
 * Format location labels for display
 */
function formatLocationLabel(location: string): string {
  return location.charAt(0).toUpperCase() + location.slice(1).toLowerCase();
}

// ============================================================================
// MAIN STATISTICS PAGE COMPONENT
// ============================================================================

function StatisticsPageContent() {
  const { discs, loading, error } = useInventory();

  // Calculate basic statistics
  const totalDiscs = discs.length;
  const totalValue = calculateCollectionValue(discs);
  const averageWeight = calculateAverageWeight(discs);
  const mostCommonManufacturer = getMostCommonManufacturer(discs);
  const averageFlightNumbers = calculateAverageFlightNumbers(discs);

  // Calculate distribution data
  const conditionCounts = countDiscsByCondition(discs);
  const locationCounts = countDiscsByLocation(discs);
  const manufacturerCounts = countDiscsByManufacturer(discs);

  // Convert to distribution format
  const conditionData = createDistributionData(
    Object.fromEntries(
      Object.entries(conditionCounts).map(([key, value]) => [
        formatConditionLabel(key),
        value,
      ])
    ),
    totalDiscs
  );

  const locationData = createDistributionData(
    Object.fromEntries(
      Object.entries(locationCounts).map(([key, value]) => [
        formatLocationLabel(key),
        value,
      ])
    ),
    totalDiscs
  );

  const manufacturerData = createDistributionData(manufacturerCounts, totalDiscs);

  // Loading state
  if (loading) {
    return (
      <Layout>
        <PageContainer title="Collection Statistics" description="Loading your collection analytics...">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </PageContainer>
      </Layout>
    );
  }

  // Error state
  if (error) {
    return (
      <Layout>
        <PageContainer title="Collection Statistics" description="Error loading your analytics">
          <EmptyState
            variant="error"
            title="Failed to load statistics"
            description={error.message}
            onAction={() => window.location.reload()}
          />
        </PageContainer>
      </Layout>
    );
  }

  // Empty collection state
  if (discs.length === 0) {
    return (
      <Layout>
        <PageContainer
          title="Collection Statistics"
          description="Start building your collection to see analytics"
          actions={
            <Button onClick={() => (window.location.href = "/inventory/add")}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Disc
            </Button>
          }
        >
          <EmptyState
            variant="default"
            title="No Statistics Available"
            description="Add discs to your collection to see comprehensive analytics and insights."
            onAction={() => (window.location.href = "/inventory/add")}
            actionLabel="Add Disc"
            secondaryActionLabel="Import Collection"
            onSecondaryAction={() => (window.location.href = "/inventory/import")}
          />
        </PageContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageContainer
        title="Collection Statistics"
        description={`Analytics for your ${totalDiscs} disc collection`}
        actions={
          <Button onClick={() => (window.location.href = "/inventory")}>
            <Disc3 className="h-4 w-4 mr-2" />
            View Collection
          </Button>
        }
      >
        {/* Key Metrics Section */}
        <Section title="Collection Overview" description="Key metrics about your disc golf collection">
          <StatCardGrid columns={4}>
            <StatCard
              title="Total Discs"
              value={totalDiscs}
              description="Discs in collection"
              icon={Disc3}
            />
            <StatCard
              title="Collection Value"
              value={totalValue > 0 ? `$${totalValue.toFixed(2)}` : "N/A"}
              description="Total purchase value"
              icon={DollarSign}
            />
            <StatCard
              title="Average Weight"
              value={averageWeight > 0 ? `${averageWeight}g` : "N/A"}
              description="Average disc weight"
              icon={Weight}
            />
            <StatCard
              title="Top Manufacturer"
              value={mostCommonManufacturer || "N/A"}
              description="Most common brand"
              icon={Star}
            />
          </StatCardGrid>
        </Section>

        {/* Flight Characteristics Section */}
        {averageFlightNumbers && (
          <Section title="Flight Characteristics" description="Average flight numbers across your collection">
            <StatCardGrid columns={4}>
              <StatCard
                title="Speed"
                value={averageFlightNumbers.speed.toFixed(1)}
                description="Average speed rating"
                icon={Zap}
              />
              <StatCard
                title="Glide"
                value={averageFlightNumbers.glide.toFixed(1)}
                description="Average glide rating"
                icon={TrendingUp}
              />
              <StatCard
                title="Turn"
                value={averageFlightNumbers.turn.toFixed(1)}
                description="Average turn rating"
                icon={Target}
              />
              <StatCard
                title="Fade"
                value={averageFlightNumbers.fade.toFixed(1)}
                description="Average fade rating"
                icon={Target}
              />
            </StatCardGrid>
          </Section>
        )}

        {/* Distribution Charts Section */}
        <Section title="Collection Distribution" description="Breakdown of your collection by various categories">
          <DistributionGrid columns={3}>
            <DistributionChart
              title="By Manufacturer"
              description="Distribution of discs by brand"
              data={manufacturerData}
              icon={Package}
              variant="bar"
              maxItems={8}
            />
            <DistributionChart
              title="By Condition"
              description="Condition state of your discs"
              data={conditionData}
              icon={Star}
              variant="bar"
              maxItems={5}
            />
            <DistributionChart
              title="By Location"
              description="Where your discs are stored"
              data={locationData}
              icon={MapPin}
              variant="bar"
              maxItems={5}
            />
          </DistributionGrid>
        </Section>
      </PageContainer>
    </Layout>
  );
}

export default function StatisticsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <StatisticsPageContent />
    </Suspense>
  );
}
